import { useState, useEffect } from "react";
import {
  X,
  Calendar,
  MapPin,
  Users,
  CheckCircle,
  Clock,
  TrendingUp,
  BarChart3,
} from "lucide-react";
import {
  <PERSON><PERSON>hart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  <PERSON><PERSON>hart,
  Pie,
  Cell,
} from "recharts";
import { useEventStore } from "../store/eventStore";
import type { Event } from "../types";
import { AnalyticsService } from "../services/analyticsService";

interface EventDashboardModalProps {
  isOpen: boolean;
  onClose: () => void;
  event: Event | null;
}

const EventDashboardModal = ({
  isOpen,
  onClose,
  event,
}: EventDashboardModalProps) => {
  const { getEventAttendees } = useEventStore();
  const [eventStats, setEventStats] = useState<any>(null);

  useEffect(() => {
    if (event) {
      const attendees = getEventAttendees(event.id);
      const stats = AnalyticsService.generateEventStats(event, attendees);

      // Additional calculations for dashboard
      const checkedInAttendees = attendees.filter((a) => a.checkedIn);
      const registrationTrend = attendees.reduce((acc, attendee) => {
        const date = new Date(attendee.registeredAt).toDateString();
        acc[date] = (acc[date] || 0) + 1;
        return acc;
      }, {} as Record<string, number>);

      const checkInTrend = checkedInAttendees.reduce((acc, attendee) => {
        if (attendee.checkedInAt) {
          const hour = new Date(attendee.checkedInAt).getHours();
          const hourKey = `${hour}:00`;
          acc[hourKey] = (acc[hourKey] || 0) + 1;
        }
        return acc;
      }, {} as Record<string, number>);

      const registrationData = Object.entries(registrationTrend)
        .map(([date, count]) => ({
          date: new Date(date).toLocaleDateString("en-US", {
            month: "short",
            day: "numeric",
          }),
          registrations: count,
        }))
        .slice(-7); // Last 7 days

      const checkInData = Array.from({ length: 24 }, (_, i) => ({
        hour: `${i}:00`,
        checkIns: checkInTrend[`${i}:00`] || 0,
      }));

      const statusData = [
        {
          name: "Checked In",
          value: checkedInAttendees.length,
          color: "#10B981",
        },
        {
          name: "Not Checked In",
          value: attendees.length - checkedInAttendees.length,
          color: "#EF4444",
        },
      ];

      setEventStats({
        ...stats,
        attendees,
        registrationData,
        checkInData,
        statusData,
        avgCheckInTime:
          checkedInAttendees.length > 0
            ? checkedInAttendees.reduce(
                (sum, a) =>
                  sum + (a.checkedInAt ? new Date(a.checkedInAt).getTime() : 0),
                0
              ) / checkedInAttendees.length
            : 0,
      });
    }
  }, [event, getEventAttendees]);

  if (!isOpen || !event || !eventStats) return null;

  const formatDate = (date: Date) => {
    return new Intl.DateTimeFormat("en-US", {
      weekday: "long",
      year: "numeric",
      month: "long",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    }).format(date);
  };

  const getStatusColor = (status: Event["status"]) => {
    switch (status) {
      case "published":
        return "bg-green-100 text-green-800";
      case "draft":
        return "bg-gray-100 text-gray-800";
      case "completed":
        return "bg-blue-100 text-blue-800";
      case "cancelled":
        return "bg-red-100 text-red-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      <div className="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        <div
          className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity"
          onClick={onClose}
        ></div>

        <span className="hidden sm:inline-block sm:align-middle sm:h-screen">
          &#8203;
        </span>

        <div className="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-6xl sm:w-full">
          <div className="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
            {/* Header */}
            <div className="flex items-center justify-between mb-6">
              <div className="flex items-center">
                <div className="mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-blue-100 sm:mx-0 sm:h-10 sm:w-10">
                  <BarChart3 className="h-6 w-6 text-blue-600" />
                </div>
                <div className="ml-4">
                  <h3 className="text-lg leading-6 font-medium text-gray-900">
                    Event Dashboard
                  </h3>
                  <p className="text-sm text-gray-500">{event.name}</p>
                </div>
              </div>
              <button
                onClick={onClose}
                className="rounded-md text-gray-400 hover:text-gray-500 focus:outline-none"
              >
                <X className="h-6 w-6" />
              </button>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
              {/* Event Info */}
              <div className="lg:col-span-1 space-y-6">
                <div className="bg-gray-50 rounded-lg p-4">
                  <h4 className="text-lg font-semibold text-gray-900 mb-3">
                    {event.name}
                  </h4>
                  <span
                    className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium mb-3 ${getStatusColor(
                      event.status
                    )}`}
                  >
                    {event.status}
                  </span>

                  <div className="space-y-3">
                    <div className="flex items-center text-sm text-gray-600">
                      <Calendar className="h-4 w-4 mr-2" />
                      <div>
                        <div>Start: {formatDate(event.startDate)}</div>
                        <div>End: {formatDate(event.endDate)}</div>
                      </div>
                    </div>
                    <div className="flex items-center text-sm text-gray-600">
                      <MapPin className="h-4 w-4 mr-2" />
                      {event.location}
                    </div>
                    <div className="flex items-center text-sm text-gray-600">
                      <Users className="h-4 w-4 mr-2" />
                      {eventStats.totalAttendees} registered
                      {event.maxAttendees && ` / ${event.maxAttendees} max`}
                    </div>
                  </div>

                  <div className="mt-4 p-3 bg-white rounded border">
                    <p className="text-sm text-gray-600 mb-2">Description</p>
                    <p className="text-sm text-gray-900">{event.description}</p>
                  </div>
                </div>

                {/* Key Metrics */}
                <div className="grid grid-cols-2 gap-4">
                  <div className="bg-blue-50 rounded-lg p-4 text-center">
                    <div className="text-2xl font-bold text-blue-600">
                      {eventStats.totalAttendees}
                    </div>
                    <div className="text-sm text-blue-600">Total Attendees</div>
                  </div>
                  <div className="bg-green-50 rounded-lg p-4 text-center">
                    <div className="text-2xl font-bold text-green-600">
                      {eventStats.checkedInCount}
                    </div>
                    <div className="text-sm text-green-600">Checked In</div>
                  </div>
                  <div className="bg-purple-50 rounded-lg p-4 text-center">
                    <div className="text-2xl font-bold text-purple-600">
                      {eventStats.checkInRate.toFixed(1)}%
                    </div>
                    <div className="text-sm text-purple-600">Check-in Rate</div>
                  </div>
                  <div className="bg-orange-50 rounded-lg p-4 text-center">
                    <div className="text-2xl font-bold text-orange-600">
                      {eventStats.totalAttendees - eventStats.checkedInCount}
                    </div>
                    <div className="text-sm text-orange-600">Pending</div>
                  </div>
                </div>
              </div>

              {/* Charts */}
              <div className="lg:col-span-2 space-y-6">
                {/* Check-in Status */}
                <div className="bg-white border rounded-lg p-4">
                  <h4 className="text-lg font-medium text-gray-900 mb-4">
                    Check-in Status
                  </h4>
                  <div className="h-48">
                    <ResponsiveContainer width="100%" height="100%">
                      <PieChart>
                        <Pie
                          data={eventStats.statusData}
                          cx="50%"
                          cy="50%"
                          outerRadius={60}
                          fill="#8884d8"
                          dataKey="value"
                          label={({ name, value }) => `${name}: ${value}`}
                        >
                          {eventStats.statusData.map(
                            (entry: any, index: number) => (
                              <Cell key={`cell-${index}`} fill={entry.color} />
                            )
                          )}
                        </Pie>
                        <Tooltip />
                      </PieChart>
                    </ResponsiveContainer>
                  </div>
                </div>

                {/* Hourly Check-ins */}
                <div className="bg-white border rounded-lg p-4">
                  <h4 className="text-lg font-medium text-gray-900 mb-4">
                    Hourly Check-ins
                  </h4>
                  <div className="h-48">
                    <ResponsiveContainer width="100%" height="100%">
                      <BarChart
                        data={eventStats.checkInData.filter(
                          (d: any) => d.checkIns > 0
                        )}
                      >
                        <CartesianGrid strokeDasharray="3 3" />
                        <XAxis dataKey="hour" />
                        <YAxis />
                        <Tooltip />
                        <Bar dataKey="checkIns" fill="#3B82F6" />
                      </BarChart>
                    </ResponsiveContainer>
                  </div>
                </div>

                {/* Registration Trend */}
                <div className="bg-white border rounded-lg p-4">
                  <h4 className="text-lg font-medium text-gray-900 mb-4">
                    Registration Trend (Last 7 Days)
                  </h4>
                  <div className="h-48">
                    <ResponsiveContainer width="100%" height="100%">
                      <BarChart data={eventStats.registrationData}>
                        <CartesianGrid strokeDasharray="3 3" />
                        <XAxis dataKey="date" />
                        <YAxis />
                        <Tooltip />
                        <Bar dataKey="registrations" fill="#10B981" />
                      </BarChart>
                    </ResponsiveContainer>
                  </div>
                </div>
              </div>
            </div>

            {/* Footer */}
            <div className="flex justify-end mt-6">
              <button
                onClick={onClose}
                className="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              >
                Close
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default EventDashboardModal;
