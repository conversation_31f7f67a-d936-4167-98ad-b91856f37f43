import { XIcon, QrCodeIcon } from "lucide-react";
import type { Attendee, Event } from "../types";
import QRCodeDisplay from "./QRCodeDisplay";

interface QRCodeModalProps {
  isOpen: boolean;
  onClose: () => void;
  attendee: Attendee | null;
  event: Event | null;
}

const QRCodeModal = ({
  isOpen,
  onClose,
  attendee,
  event,
}: QRCodeModalProps) => {
  if (!isOpen || !attendee || !event) return null;

  const handleEmailSent = () => {
    // Could update attendee status or show notification
    console.log("QR code email sent successfully");
  };

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      <div className="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        <div
          className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity"
          onClick={onClose}
        ></div>

        <span className="hidden sm:inline-block sm:align-middle sm:h-screen">
          &#8203;
        </span>

        <div className="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
          <div className="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
            <div className="flex items-center justify-between mb-6">
              <div className="flex items-center">
                <div className="mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-blue-100 sm:mx-0 sm:h-10 sm:w-10">
                  <QrCodeIcon className="h-6 w-6 text-blue-600" />
                </div>
                <div className="ml-4">
                  <h3 className="text-lg leading-6 font-medium text-gray-900">
                    QR Code
                  </h3>
                  <p className="text-sm text-gray-500">{event.name}</p>
                </div>
              </div>
              <button
                onClick={onClose}
                className="rounded-md text-gray-400 hover:text-gray-500 focus:outline-none"
              >
                <XIcon className="h-6 w-6" />
              </button>
            </div>

            <div className="mt-4">
              <QRCodeDisplay
                attendee={attendee}
                event={event}
                size="medium"
                showActions={true}
                onEmailSent={handleEmailSent}
              />
            </div>

            <div className="mt-6 bg-blue-50 border border-blue-200 rounded-lg p-4">
              <h4 className="text-sm font-medium text-blue-900 mb-2">
                How to use this QR code:
              </h4>
              <ul className="text-sm text-blue-800 space-y-1">
                <li>• Save the QR code image to your device</li>
                <li>• Present it at the event entrance for scanning</li>
                <li>• The QR code contains encrypted attendee information</li>
                <li>• Each QR code is unique and cannot be duplicated</li>
                <li>• QR codes expire 24 hours after generation</li>
              </ul>
            </div>

            <div className="flex justify-end mt-6">
              <button
                onClick={onClose}
                className="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              >
                Close
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default QRCodeModal;
