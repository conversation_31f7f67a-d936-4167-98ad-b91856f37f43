import { useState, useMemo } from "react";
import {
  X,
  Search,
  Users,
  CheckCircle,
  XCircle,
  Download,
  Phone,
  Mail,
  User,
} from "lucide-react";
import { useEventStore } from "../store/eventStore";
import type { Event, Attendee } from "../types";

interface DoorlistModalProps {
  isOpen: boolean;
  onClose: () => void;
  event: Event | null;
}

const DoorlistModal = ({ isOpen, onClose, event }: DoorlistModalProps) => {
  const { getEventAttendees, checkInAttendee } = useEventStore();
  const [searchTerm, setSearchTerm] = useState("");
  const [filterStatus, setFilterStatus] = useState<
    "all" | "checked-in" | "not-checked-in"
  >("all");

  if (!isOpen || !event) return null;

  const attendees = getEventAttendees(event.id);

  // Filter and sort attendees
  const filteredAttendees = useMemo(() => {
    let filtered = attendees.filter((attendee) => {
      const matchesSearch =
        attendee.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        attendee.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
        (attendee.phone &&
          attendee.phone.toLowerCase().includes(searchTerm.toLowerCase()));

      const matchesStatus =
        filterStatus === "all" ||
        (filterStatus === "checked-in" && attendee.checkedIn) ||
        (filterStatus === "not-checked-in" && !attendee.checkedIn);

      return matchesSearch && matchesStatus;
    });

    // Sort alphabetically by name
    return filtered.sort((a, b) => a.name.localeCompare(b.name));
  }, [attendees, searchTerm, filterStatus]);

  const stats = {
    total: attendees.length,
    checkedIn: attendees.filter((a) => a.checkedIn).length,
    notCheckedIn: attendees.filter((a) => !a.checkedIn).length,
  };

  const handleCheckIn = async (attendeeId: string) => {
    await checkInAttendee(attendeeId);
  };

  const exportDoorlist = () => {
    const csvContent = [
      ["Name", "Email", "Phone", "Status", "Check-in Time"].join(","),
      ...filteredAttendees.map((attendee) =>
        [
          `"${attendee.name}"`,
          `"${attendee.email}"`,
          `"${attendee.phone || "N/A"}"`,
          attendee.checkedIn ? "Checked In" : "Not Checked In",
          attendee.checkedInAt
            ? new Date(attendee.checkedInAt).toLocaleString()
            : "N/A",
        ].join(",")
      ),
    ].join("\n");

    const blob = new Blob([csvContent], { type: "text/csv" });
    const url = URL.createObjectURL(blob);
    const link = document.createElement("a");
    link.href = url;
    link.download = `doorlist-${event.name.replace(/\s+/g, "-")}.csv`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  };

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      <div className="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        <div
          className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity"
          onClick={onClose}
        ></div>

        <span className="hidden sm:inline-block sm:align-middle sm:h-screen">
          &#8203;
        </span>

        <div className="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-6xl sm:w-full">
          <div className="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
            {/* Header */}
            <div className="flex items-center justify-between mb-6">
              <div className="flex items-center">
                <div className="mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-blue-100 sm:mx-0 sm:h-10 sm:w-10">
                  <Users className="h-6 w-6 text-blue-600" />
                </div>
                <div className="ml-4">
                  <h3 className="text-lg leading-6 font-medium text-gray-900">
                    Doorlist - {event.name}
                  </h3>
                  <p className="text-sm text-gray-500">
                    Attendee check-in management
                  </p>
                </div>
              </div>
              <button
                onClick={onClose}
                className="rounded-md text-gray-400 hover:text-gray-500 focus:outline-none"
              >
                <X className="h-6 w-6" />
              </button>
            </div>

            {/* Stats */}
            <div className="grid grid-cols-3 gap-4 mb-6">
              <div className="bg-blue-50 rounded-lg p-4 text-center">
                <div className="text-2xl font-bold text-blue-600">
                  {stats.total}
                </div>
                <div className="text-sm text-blue-600">Total</div>
              </div>
              <div className="bg-green-50 rounded-lg p-4 text-center">
                <div className="text-2xl font-bold text-green-600">
                  {stats.checkedIn}
                </div>
                <div className="text-sm text-green-600">Checked In</div>
              </div>
              <div className="bg-red-50 rounded-lg p-4 text-center">
                <div className="text-2xl font-bold text-red-600">
                  {stats.notCheckedIn}
                </div>
                <div className="text-sm text-red-600">Not Checked In</div>
              </div>
            </div>

            {/* Filters */}
            <div className="flex flex-col sm:flex-row gap-4 mb-6">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                  <input
                    type="text"
                    placeholder="Search by name, email, or phone..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10 pr-4 py-2 w-full border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>
              </div>
              <div className="flex gap-2">
                <select
                  value={filterStatus}
                  onChange={(e) => setFilterStatus(e.target.value as any)}
                  className="px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                >
                  <option value="all">All Status</option>
                  <option value="checked-in">Checked In</option>
                  <option value="not-checked-in">Not Checked In</option>
                </select>
                <button
                  onClick={exportDoorlist}
                  className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
                >
                  <Download className="h-4 w-4 mr-2" />
                  Export
                </button>
              </div>
            </div>

            {/* Attendee List */}
            <div className="max-h-96 overflow-y-auto border border-gray-200 rounded-lg">
              {filteredAttendees.length === 0 ? (
                <div className="text-center py-8 text-gray-500">
                  <Users className="h-12 w-12 mx-auto mb-2 text-gray-300" />
                  <p>No attendees found</p>
                </div>
              ) : (
                <div className="divide-y divide-gray-200">
                  {filteredAttendees.map((attendee) => (
                    <div key={attendee.id} className="p-4 hover:bg-gray-50">
                      <div className="flex items-center justify-between">
                        <div className="flex-1 min-w-0">
                          <div className="flex items-center space-x-3">
                            <div className="flex-shrink-0">
                              <div className="w-10 h-10 bg-gray-200 rounded-full flex items-center justify-center">
                                <User className="h-5 w-5 text-gray-500" />
                              </div>
                            </div>
                            <div className="flex-1 min-w-0">
                              <p className="text-sm font-medium text-gray-900 truncate">
                                {attendee.name}
                              </p>
                              <div className="flex items-center space-x-4 mt-1">
                                <div className="flex items-center text-sm text-gray-500">
                                  <Mail className="h-3 w-3 mr-1" />
                                  {attendee.email}
                                </div>
                                {attendee.phone && (
                                  <div className="flex items-center text-sm text-gray-500">
                                    <Phone className="h-3 w-3 mr-1" />
                                    {attendee.phone}
                                  </div>
                                )}
                              </div>
                            </div>
                          </div>
                        </div>
                        <div className="flex items-center space-x-3">
                          <div className="text-right">
                            {attendee.checkedIn ? (
                              <div className="flex items-center text-green-600">
                                <CheckCircle className="h-4 w-4 mr-1" />
                                <span className="text-sm font-medium">
                                  Checked In
                                </span>
                              </div>
                            ) : (
                              <button
                                onClick={() => handleCheckIn(attendee.id)}
                                className="flex items-center text-blue-600 hover:text-blue-800"
                              >
                                <XCircle className="h-4 w-4 mr-1" />
                                <span className="text-sm font-medium">
                                  Check In
                                </span>
                              </button>
                            )}
                            {attendee.checkedInAt && (
                              <div className="text-xs text-gray-500 mt-1">
                                {new Date(
                                  attendee.checkedInAt
                                ).toLocaleString()}
                              </div>
                            )}
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>

            {/* Footer */}
            <div className="flex justify-between items-center mt-6">
              <div className="text-sm text-gray-500">
                Showing {filteredAttendees.length} of {attendees.length}{" "}
                attendees
              </div>
              <button
                onClick={onClose}
                className="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              >
                Close
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default DoorlistModal;
